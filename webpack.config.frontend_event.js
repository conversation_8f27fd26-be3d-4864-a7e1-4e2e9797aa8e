const path = require('node:path');
const glob = require('glob');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const VENDOR_ENTRIES = ['./frontend_event/vendor.js'];

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: {
        vendor: VENDOR_ENTRIES,
        main: glob.sync([
            './frontend_event/sport-wrench.module.js',
            './frontend_event/**/*.js',
        ], { absolute: true, ignore: VENDOR_ENTRIES }),
    },

    plugins: [
        new HtmlWebpackPlugin({
            template: './frontend_event/sport-wrench.html',
            filename: 'index.html',
            inject: false,
            minify: false,
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    context: './frontend_event/',
                    from: '**/*.html',
                    to: '[path][name][ext]',
                    globOptions: {
                        ignore: ['**/sport-wrench.html', '**/index.html'] // Exclude template files
                    }
                },
            ],
        }),
    ],

    devServer: {
        port: 8078,
    },
};
