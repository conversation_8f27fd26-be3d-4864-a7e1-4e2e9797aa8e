import 'angular-loading-bar/build/loading-bar.min.css'
import 'font-awesome/css/font-awesome.css'
import 'ng-table/dist/ng-table.css' // bower ng-table@0.5.5 -> npm ng-table@0.5.4
import 'angular-toastr/dist/angular-toastr.css'
import 'ui-select/dist/select.css' // bower angular-ui-select@0.19.8 -> npm ui-select@0.19.8
import 'angular-bootstrap-colorpicker/css/colorpicker.min.css'

import '../assets/styles/main.scss'
import '../assets/styles/admin.scss'

import '../assets/js/polyfills/babel-polyfill.js'
import '../assets/js/polyfills/array-includes.js'
import '../assets/js/polyfills/string-includes.js'

import 'angular-sanitize'
import 'angular-ui-router'
import 'ngstorage'
import '../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js'
import 'angular-ui-utils/modules/mask/mask.js'
import 'oclazyload'
import 'angular-loading-bar'
import 'sticky-table-headers' // bower StickyTableHeaders@0.1.24 -> npm sticky-table-headers@0.1.24
import 'ng-table/dist/ng-table.js' // bower ng-table@0.5.5 -> npm ng-table@0.5.4
import 'angular-clipboard'
import 'ng-infinite-scroll/build/ng-infinite-scroll.js' // bower ngInfiniteScroll@1.0.0 -> npm ng-infinite-scroll@1.0.0
import 'bootstrap-ui-datetime-picker'
import 'angular-animate'
import 'angular-toastr'
import 'ui-select' // bower angular-ui-select@0.19.8 -> npm ui-select@0.19.8
import 'angular-bootstrap-colorpicker'
import 'angularjs-dropdown-multiselect'
import '../assets/js/signature_pad/signature_pad.js'
import 'angular-simple-logger'
