// CSS imports
import 'angular-loading-bar/build/loading-bar.min.css'
import 'font-awesome/css/font-awesome.css'
import 'angular-toastr/dist/angular-toastr.css'

// Polyfills
import '../assets/js/polyfills/babel-polyfill.js'
import '../assets/js/polyfills/array-includes.js'
import '../assets/js/polyfills/string-includes.js'

// Angular modules (core angular, underscore, jquery, moment loaded via CDN)
import 'angular-resource'
import 'angular-sanitize'
import 'angular-cookies'
import 'angular-ui-router'
import 'ngstorage' // bower ngstorage@0.3.0 -> npm ngstorage@0.3.11
import 'angular-ui-bootstrap' // bower angular-bootstrap@0.13.4 -> npm angular-ui-bootstrap@0.13.4

// Bower-only packages (copied from bower_components)
import '../assets/bower_components/angular-utils-ui-breadcrumbs/uiBreadcrumbs.js' // bower angular-utils-ui-breadcrumbs@0.2.1
import 'angular-loading-bar'
import '../assets/bower_components/angular-dragdrop/src/angular-dragdrop.min.js' // bower angular-dragdrop@1.0.12
import '../assets/bower_components/ngSelectable/src/ngSelectable.js' // bower ngSelectable
import 'angular-clipboard' // bower angular-clipboard@1.7.0 -> npm angular-clipboard@1.6.2
import 'angular-toastr'

// Custom validation scripts (from assets)
// Note: jQuery UI will be loaded via CDN in HTML template
import '../assets/js/validation/angular-validation.js'
import '../assets/js/validation/angular-validation-rule.js'
