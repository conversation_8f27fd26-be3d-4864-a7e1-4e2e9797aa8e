const path = require('node:path');
const glob = require('glob');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require("html-webpack-plugin");

const VENDOR_ENTRIES = ['./frontend_admin/vendor.js'];

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: {
        vendor: VENDOR_ENTRIES,
        main: glob.sync([
            './frontend_admin/sport-wrench-admin.module.js',
            './frontend_admin/**/*.js',
        ], { absolute: true, ignore: VENDOR_ENTRIES }),
    },

    plugins: [
        new HtmlWebpackPlugin({
            template: './frontend_admin/sport-wrench-admin.html',
            filename: 'index.html',
            inject: false,
            minify: false,
        }),
        new CopyWebpackPlugin({
            patterns: [
                { context: './frontend_admin/',  from: '**/*.html',  to: '[path][name][ext]' },
            ],
        }),
    ],

    devServer: {
        port: 8087,
    },
};
