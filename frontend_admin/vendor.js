// CSS imports
import 'angular-loading-bar/build/loading-bar.min.css'
import 'font-awesome/css/font-awesome.css'
import 'ng-table/dist/ng-table.css' // bower ng-table@0.5.5 -> npm ng-table@0.5.4
import 'angular-toastr/dist/angular-toastr.css'

// Polyfills
import '../assets/js/polyfills/babel-polyfill.js'
import '../assets/js/polyfills/array-includes.js'
import '../assets/js/polyfills/string-includes.js'

// Angular modules (core angular, underscore, jquery, moment loaded via CDN)
import 'angular-resource'
import 'angular-sanitize'
import 'angular-cookies'
import 'angular-ui-router'
import 'ngstorage' // bower ngstorage@0.3.0 -> npm ngstorage@0.3.11

// Custom UI Bootstrap (from assets)
import '../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js'

// Bower-only packages (copied from bower_components)
import '../assets/bower_components/angular-utils-ui-breadcrumbs/uiBreadcrumbs.js' // bower angular-utils-ui-breadcrumbs@0.2.1
import 'bootstrap-ui-datetime-picker'
import 'ng-table/dist/ng-table.js' // bower ng-table@0.5.5 -> npm ng-table@0.5.4
import 'sticky-table-headers' // bower StickyTableHeaders@0.1.12 -> npm sticky-table-headers@0.1.24
import 'angular-clipboard' // bower angular-clipboard@1.7.0 -> npm angular-clipboard@1.6.2
import 'angular-loading-bar'
import 'angular-toastr'

// Validation scripts (from assets)
import '../assets/js/validation/angular-validation.js'
import '../assets/js/validation/angular-validation-rule.js'

// Bower-only packages (copied from bower_components)
import '../assets/bower_components/angular-dragdrop/src/angular-dragdrop.min.js' // bower angular-dragdrop@1.0.12
import '../assets/bower_components/ngSelectable/src/ngSelectable.js' // bower ngSelectable
