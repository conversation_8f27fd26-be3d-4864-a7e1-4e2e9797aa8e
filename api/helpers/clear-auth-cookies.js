module.exports = {
    friendlyName: 'Clear authentication cookies',
    description: 'Clear authentication cookies with proper security options',

    inputs: {
        res: {
            type: 'ref',
            description: 'The response object',
            required: true
        }
    },

    fn: async function (inputs) {
        const { res } = inputs;
        
        const cookieOptions = {
            httpOnly: true,
            secure: sails.config.environment === 'production'
        };
        
        // Clear remember me cookie
        res.clearCookie('remember_me', cookieOptions);
        
        // Clear session cookie with domain if configured
        const sessionCookieOptions = { ...cookieOptions };
        if (sails.config.session && sails.config.session.cookie && sails.config.session.cookie.domain) {
            sessionCookieOptions.domain = sails.config.session.cookie.domain;
        }
        res.clearCookie('sw.sid', sessionCookieOptions);
    }
};
