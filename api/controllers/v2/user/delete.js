module.exports = {
    friendlyName: 'Delete user account',
    description: 'Delete user account',

    exits: {
        accepted: {
            statusCode: 202,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);

        try {
            await UserService.reg.deleteUser(userId);

            await SessionService.clearSession(this.req, this.res);

            exits.accepted({ message: 'OK' });
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};
